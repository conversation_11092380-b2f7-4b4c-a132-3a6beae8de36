{"name": "@astro/render", "version": "0.0.2", "description": "", "main": "dist/emp.json", "type": "module", "scripts": {"dev": "emp dev", "dev:unpkg": "emp dev --env unpkg", "dev:bos": "emp dev --env bos", "build": "emp build", "build:unpkg": "emp build --env unpkg", "deploy:bos": "emp build  --env bos", "start": "emp serve", "stat": "emp build --analyze", "build:doctor": "emp build --doctor", "dev:doctor": "emp dev --doctor", "lint": "biome check . --fix"}, "maintainers": ["x<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hupanpan2", "wangchunyang2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chenweihao", "chen<PERSON>lin", "chen<PERSON><PERSON>", "tangjiaqiang2", "l<PERSON><PERSON><PERSON><PERSON>"], "files": ["dist", "output", "package.json", "README.md"], "publishConfig": {"registry": "https://npm-registry.yy.com"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@empjs/biome-config": "^0.7.2", "@empjs/cli": "3.6.0", "@empjs/plugin-react": "3.6.0", "@types/react": "^18.2.30", "@types/react-dom": "^18.2.14", "react-router-dom": "6", "typescript": "^5.7.2"}, "dependencies": {"@astro/utils": "^0.0.5", "@empjs/share": "3.6.0", "abortcontroller-polyfill": "^1.7.6", "valtio": "^2.1.4"}}