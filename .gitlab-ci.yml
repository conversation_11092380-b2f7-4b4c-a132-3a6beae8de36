image: harbor.yy.com/front_end/emp:node20.11pnpm8.15
cache:
  key: ${CI_JOB_NAME}
  paths:
    - node_modules/

deploy_unpkg:
  script:
    - echo -e "\n10.18.111.211 registry.npmjs.org registry.yarnpkg.com" >> /etc/hosts
    - pnpm i
    - pnpm add -D @hd/pkgcare
    - ./node_modules/.bin/pkgcare inc && pnpm build:unpkg && npm publish && ./node_modules/.bin/pkgcare push
  
  when: manual


deploy_bos:

  script:
    - echo -e "\n10.18.111.211 registry.npmjs.org registry.yarnpkg.com" >> /etc/hosts
    - pnpm i
    - pnpm deploy:bos
  
  when: manual
  artifacts:
    expire_in: 1 week
    paths:
      - dist
      - output
      - package.json



