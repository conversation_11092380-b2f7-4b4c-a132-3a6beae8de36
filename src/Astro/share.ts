import {reactAdapter} from '@empjs/share/adapter'
import empRuntime from '@empjs/share/runtime'

export {empRuntime, reactAdapter}
class EmpShare {
  empCache: string[] = []
  constructor() {
    this.init()
  }
  init() {
    empRuntime.init({
      shared: reactAdapter.shared,
      remotes: [],
      name: 'AstroRender',
    })
  }
  register(url: string, scope: string) {
    if (this.empCache.indexOf(scope) === -1) {
      empRuntime.register([
        {
          entry: url,
          name: scope,
        },
      ])
      this.empCache.push(scope)
    }
  }
  async load(scope: string, path: string, exportName = 'default') {
    const cpath = path.replace('.', scope)
    const cb = await empRuntime.load(cpath)
    return cb[exportName]
  }
}
const astroShare = new EmpShare()
export default astroShare
//
const win = window as any
win.Astro_Share = astroShare
