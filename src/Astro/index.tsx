import {Suspense, useCallback, useEffect, useMemo} from 'react'
import 'src/css/index.scss'
import ErrorBoundary from 'src/components/ErrorBoundary/index'
import type defaultAstroStore from './astroStore'
import {createAstroStore} from './astroStore'

interface AstroProps {
  astroStore: typeof defaultAstroStore // 移除可选标记 ?
}

export const AstroLayout = ({astroStore}: AstroProps) => {
  console.log(`AstroLayout`)
  const astroState = astroStore.state
  const {device} = astroState

  const Page = useCallback(() => {
    const astroState = astroStore.state
    const d = astroState.page
    const slots = getSlotsComponents(astroState.slotsConfig)

    return d ? (
      <Component
        slots={slots}
        url={d.url}
        scope={d.scope}
        path={d.path}
        dataConfig={d.dataConfig ? astroStore.clone(d.dataConfig) : {}}
        key={`${d.url}@${d.path}`}
        renderType="page"
        sort={-1}
      />
    ) : (
      <></>
    )
  }, [astroStore])

  const getSlotsComponents = useCallback(
    (slotsConfig: any[]) => {
      const slots: {[k: string]: any} = {}
      slotsConfig.map((d: any) => {
        if (!slots[d.slotKey]) slots[d.slotKey] = []
        slots[d.slotKey].push(<SlotComponent key={`${d.slotKey}-${d.uniqueNum}`} config={d} />)
      })
      return slots
    },
    [astroStore],
  )

  const SlotComponent = useCallback(
    ({config: d}: {config: any}) => {
      if (!d) return null
      return (
        <Component
          url={d.url}
          scope={d.scope}
          path={d.path}
          dataConfig={d.dataConfig ? astroStore.clone(d.dataConfig) : {}}
          key={d.uniqueNum}
          slotKey={d.slotKey}
          renderType="slot"
          sort={d.sort}
          uniqueNum={d.uniqueNum}
          subComponents={d.subComponents}
        />
      )
    },
    [astroStore],
  )

  const Components = useCallback(
    (props: any) => {
      const astroState = astroStore.state
      return (
        <>
          {astroState.childrenConfig.map((d: any) => (
            <Component
              url={d.url}
              scope={d.scope}
              path={d.path}
              dataConfig={d.dataConfig || {}}
              key={d.uniqueNum}
              renderType="component"
              sort={d.sort}
              uniqueNum={d.uniqueNum}
              subComponents={d.subComponents}
            />
          ))}
        </>
      )
    },
    [astroStore],
  )

  const Component = useCallback(
    (props: any) => {
      const {url, scope, path, dataConfig} = props
      const astroState = astroStore.state
      const keyName = `${url}@${path}`

      useEffect(() => {
        astroState.getComponent({url, scope, path})
      }, [keyName])

      return (
        <ErrorBoundary>
          <ViewModeComp
            scope={scope}
            path={path}
            astroState={astroState}
            sort={props.sort}
            renderType={props.renderType}
            uniqueNum={props.uniqueNum}
          >
            <Suspense>
              {astroState.cls[keyName] && (
                <ViewComp
                  ComponentItem={astroState.cls[keyName]}
                  slots={props.slots}
                  dataConfig={dataConfig}
                  subComponents={props.subComponents}
                >
                  <Components />
                </ViewComp>
              )}
            </Suspense>
          </ViewModeComp>
        </ErrorBoundary>
      )
    },
    [astroStore],
  )

  const ViewComp = useCallback(
    ({ComponentItem, slots, dataConfig, children, subComponents}: any) => {
      const subSlots = getSlotsComponents(subComponents ?? [])
      const useSubSlots = Object.keys(subSlots).length > 0
      return (
        <ComponentItem slots={useSubSlots ? subSlots : slots} {...dataConfig}>
          {children}
        </ComponentItem>
      )
    },
    [astroStore],
  )

  const ViewModeComp = useCallback(
    (props: any) => {
      if (!props.astroState.viewMode) return props.children
      //
      const {astroState} = props
      const isAct = props.sort === astroState.page.componentKey && astroState.page.componentKey != -1
      useEffect(() => {
        setTimeout(() => {
          const element = document.getElementById('__astro__selected')
          if (element) element.scrollIntoView({behavior: 'smooth', block: 'start', inline: 'nearest'})
        }, 10)
      }, [astroState.page.componentKey])
      return <span id={isAct ? '__astro__selected' : ''}>{props.children}</span>
    },
    [astroStore],
  )

  useEffect(() => {
    let rootFontSize = astroState.page.ext?.rootFontSize
    if (rootFontSize) {
      rootFontSize = JSON.parse(rootFontSize)
      document.querySelector('html')!.setAttribute('style', `font-size: ${rootFontSize[device]};`)
    }
    astroState.setFontSize(true)
  }, [astroState.page.ext?.rootFontSize])

  return <>{astroState.isSetFontSize && (!astroState.page.url ? <Components /> : <Page />)}</>
}

export default AstroLayout
