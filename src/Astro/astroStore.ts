import config from 'src/config'
import deviceServer from 'src/helper/device'
import {BaseStore} from 'src/store'
import type {AllConfigOptions, ProjectType, ViewModeType} from 'src/types'
import share from './share'
// 在文件顶部添加计数器
let instanceCounter = 0

export class AstroStore extends BaseStore {
  readonly instanceId: number

  constructor() {
    super()
    this.instanceId = ++instanceCounter
  }

  page: any = {}
  slotsConfig: any = []
  childrenConfig: any = []
  viewMode: ViewModeType = 'production'
  isSetFontSize = false
  project!: ProjectType
  device!: string
  hasError = false
  setup({page, project, device}: any, viewMode: ViewModeType = 'production') {
    this.page = page
    this.project = project
    this.viewMode = viewMode
    this.slotsConfig = (page.components || []).filter((v: any) => !!v.slotKey)
    this.childrenConfig = (page.components || []).filter((v: any) => !v.slotKey)
    this.device = deviceServer.setPageMode(device)
    // 设置项目信息到window，供组件获取相关actId数据
    ;(window as any).Astro_Project_Info = project
    //
    if (page.name && !this?.project?.tags?.includes(4)) {
      const title = page.name
      document.title = title
    }
  }

  async getPageData({mid, pid, versionId = 0, env}: AllConfigOptions) {
    const configData = await config.materialApi.get('/actProject/pageConfig', {
      projectId: mid,
      env: env ? env : config.env,
      page: pid,
      versionId,
    })
    return configData
  }

  async getPage({mid, pid, versionId = 0, env, device}: AllConfigOptions) {
    const configData = await this.getPageData({mid, pid, versionId, env})
    if (configData?.result === 200 && configData?.data) {
      const {data, project} = configData
      this.setup({page: data, project, device})
    }
  }

  cls: {[k: string]: any} = {}
  clsLoad = false
  async getComponent({url, scope, path, exportName = 'default'}: any) {
    const {cls} = this
    const keyName = `${url}@${path}`
    if (!cls[keyName]) {
      this.clsLoad = true
      share.register(url, scope)
      cls[keyName] = await share.load(scope, path, exportName)
      this.clsLoad = false
    }
    return cls[keyName]
  }

  setFontSize(bool: boolean) {
    this.isSetFontSize = bool
  }
}
export default new AstroStore()

export const createAstroStore = () => {
  const as = new AstroStore()
  console.log(`@@#`, as)
  return as
}
