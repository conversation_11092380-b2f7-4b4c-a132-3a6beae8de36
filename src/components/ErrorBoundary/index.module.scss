.error_boundary {
    height: 100vh;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;

    h1 {
        font-size: 80px;
        font-weight: bold;
    }
}

.detail_btn {
  margin: 10px 0;
  padding: 5px 15px;
  background: transparent;
  border: 1px solid #000;
  color: #000;
  cursor: pointer;
  border-radius: 4px;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}

.error_stack {
  margin-top: 10px;
  background: rgb(254 246 213);
  border-radius: 4px;
  font-size: 12px;
  text-align: left;
  padding: 10px;
  word-break: break-word;
}