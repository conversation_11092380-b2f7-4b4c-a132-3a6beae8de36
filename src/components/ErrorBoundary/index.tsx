import React from 'react'
import astroStore from 'src/Astro/astroStore'

import sm from './index.module.scss'
class ErrorBoundary extends React.Component {
  constructor(props: any) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false
    }
  }

  static getDerivedStateFromError(error: any) {
    return {hasError: true}
  }

  componentDidCatch(error: any, errorInfo: any) {
    // 你同样可以将错误日志上报给服务器
    console.warn('[Astro Render ErrorBoundary] error:', error)
    console.warn(`[Astro Render ErrorBoundary] errorInfo:`, errorInfo)
    this.setState({
      error,
      errorInfo
    })
  }

  toggleDetails = () => {
    this.setState((prevState: any) => ({
      showDetails: !prevState.showDetails
    }))
  }

  render() {
    if ((this.state as any).hasError) {
      astroStore.hasError = true
      return (
        <div className={sm.error_boundary}>
          <div>
            <h1>组件异常</h1>
            <p>{(this.state as any).error && (this.state as any).error.toString()}</p>
            <button onClick={this.toggleDetails} className={sm.detail_btn}>
              {(this.state as any).showDetails ? '隐藏详情' : '显示详情'}
            </button>
            {(this.state as any).showDetails && (
              <div className={sm.error_stack}>
                {(this.state as any).errorInfo && (this.state as any).errorInfo.componentStack}
              </div>
            )}
          </div>
        </div>
      )
    }
    return (this.props as any).children
  }
}

export default ErrorBoundary
