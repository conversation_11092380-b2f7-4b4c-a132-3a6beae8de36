export type EmpLoadConfigType = {
  scope: string
  url: string
  path: string
}
export type ViewModeType = 'preview' | 'production'
export type ProjectType = {
  projectId: number
  actId: number
  actName: string
  avatar: string
  beginTime: number
  endTime: number
  tags: number[]
  business: number[]
  description: string
}
export type EnvType = 'test' | 'prod'
export type PageConfigOptions = {
  /**
   * 应用id
   */
  mid: string
  /**
   * 页面id
   */
  pid: string
  /**
   * 版本id
   */
  versionId: string | number
  env?: EnvType
}

export type AllConfigOptions = PageConfigOptions & {
  env?: string //环境
  device?: string // 设备
}
