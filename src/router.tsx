import {RouterProvider, createBrowserRouter} from 'react-router-dom'
import {AstroApp} from 'src/pages/Astro'
import {Preview} from 'src/pages/Astro/preview'
import {NoMatch} from 'src/pages/NoMatch'
//
async function loader({request}: any) {
  const url = new URL(request.url)
  const versionId = url.searchParams.get('versionId') || ''
  const env = url.searchParams.get('env') || ''
  const device = url.searchParams.get('device') || ''
  return {env, versionId, device}
}
//
const router = createBrowserRouter([
  {
    path: '/astro/:mid/:pid?/:router_name?',
    element: <AstroApp />,
    loader,
  },
  {
    path: '/astro/preview/:router_name?',
    element: <Preview />,
    loader,
  },
  {path: '*', element: <NoMatch />},
  
])

export default () => {
  return <RouterProvider router={router} />
}
