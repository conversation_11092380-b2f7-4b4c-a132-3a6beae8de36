import {Http} from '@astro/utils'
export const getParam = (key: string, url = window.location.href) => {
  const r = new RegExp('(\\?|#|&)' + key + '=([^&#]*)(&|#|$)')
  const m = url.match(r)
  return decodeURIComponent(!m ? '' : m[2])
}
export const host = window.location.host
const defaultEnv = process.env.mode
export const is_test = defaultEnv === 'development' || host.indexOf('-test') != -1 || getParam('test')

export class Config {
  public env = is_test ? 'test' : 'prod'
  public setEnv(env: 'test' | 'prod' = 'test') {
    this.env = env
  }
  public get material() {
    return this.env === 'test'
      ? 'https://test-ihdpt.yy.com/web/template'
      : 'https://ihdpt.yystatic.com/web/template'
  }
  private httpInstance: {[k: string]: Http} = {}
  public get materialApi() {
    const kn = `materialApi-${this.env}`
    if (!this.httpInstance[kn]) {
      this.httpInstance[kn] = new Http({base: this.material, credentials: 'omit'})
    }
    return this.httpInstance[kn]
  }
}
export default new Config()
