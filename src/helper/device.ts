type DeviceOptions = {
  useUA?: boolean
}

class Device {
  // [x: string]: any
  useUA = true
  /* setup(o?: DeviceOptions) {
    this.useUA = !!o?.useUA
    //
    this.setPageMode()
    return this
  } */
  setPageMode(device?: string | null) {
    // if (!device) {
    //   const urlParams = new URLSearchParams(window.location.search)
    //   device = urlParams.get('device')
    // }
    const win: any = window
    if (device) {
      win.pageMode = device
    } else if (!win.pageMode) {
      if (this.useUA === true && this.isMobile === true) {
        // location.href = 'mobile.html'
        win.pageMode = 'h5'
      } else if (this.useUA === true && this.isMobile === false) {
        // location.href = 'index.html'
        win.pageMode = 'pc'
      }
    } else {
      if (this.useUA === true && this.isMobile === true && win.pageMode === 'pc') {
        // location.href = 'mobile.html'
        win.pageMode = 'h5'
      } else if (this.useUA === true && this.isMobile === false && win.pageMode === 'h5') {
        // location.href = 'index.html'
        win.pageMode = 'pc'
      }
    }
    return win.pageMode
  }
  get isMobile() {
    const regex = /Mobi|Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i
    return regex.test(navigator.userAgent)
  }
}
export default new Device()
