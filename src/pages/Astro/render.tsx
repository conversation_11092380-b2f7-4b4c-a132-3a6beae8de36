import {useEffect, useMemo} from 'react'
import {AstroLayout} from 'src/Astro'
import { createAstroStore } from 'src/Astro/astroStore'
import config from 'src/config'
import type {PageConfigOptions} from 'src/types'

const createAstroApp = () => {
  
  return ({mid, pid, versionId, env}: PageConfigOptions) => {
    
    const astroStore = useMemo(() => createAstroStore(), [])
    console.log('call createAstroApp', astroStore.instanceId, 'mid:', mid, 'pid:', pid)
    useEffect(() => {
      config.setEnv(env)
      astroStore.getPage({mid, pid, versionId, env})
    }, [mid, pid])

    return <AstroLayout astroStore={astroStore} />
  }
}

export const AstroApp = createAstroApp()
// export default createAstroApp()
