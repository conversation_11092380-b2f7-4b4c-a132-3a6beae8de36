import {useEffect, useMemo} from 'react'
import {useLoaderData, useParams} from 'react-router-dom'
import {AstroLayout} from 'src/Astro'
import {createAstroStore} from 'src/Astro/astroStore'

export const AstroApp = () => {
  const astroStore = useMemo(() => createAstroStore(), [])
  const {mid, pid} = useParams() as any
  const {env, versionId, device}: any = useLoaderData()
  useEffect(() => {
    astroStore.getPage({mid, pid, env, versionId, device})
  }, [mid, pid])

  return <AstroLayout astroStore={astroStore} />
}

export default AstroApp
