import {useEffect, useMemo} from 'react'
import {useLoaderData} from 'react-router-dom'
import {AstroLayout} from 'src/Astro'
import {createAstroStore} from 'src/Astro/astroStore'
export const Preview = () => {
  const astroStore = useMemo(() => createAstroStore(), [])
  const astroState = astroStore.state
  const {device}: any = useLoaderData()
  useEffect(() => {
    window.addEventListener(
      'message',
      async (e: any) => {
        // 离线包需要获取preview当前引入的资源
        if (e.data && e.data.type === 'checkResource') {
          const resourceEntries = performance.getEntriesByType('resource')
          const resourceNameArray = resourceEntries.map(entry => entry.name)
          window.parent.postMessage({type: 'previewResource', payload: resourceNameArray}, '*')
        } else if (e.data && e.data.page) {
          astroState.setup({...e.data, device}, 'preview')
        }
      },
      false,
    )
    return () => {
      window.removeEventListener('message', () => {})
    }
  }, [])
  return <AstroLayout astroStore={astroStore} />
}
