import {defineConfig} from '@empjs/cli'
import ReactPlugin from '@empjs/plugin-react'
import {externalReact, pluginRspackEmpShare} from '@empjs/share'

export default defineConfig(store => {
  const env: 'bos' | 'unpkg' | undefined = store.cliOptions.env
  let base = ''
  switch (env) {
    case 'bos': {
      base = '/astro/'
      break
    }
    case 'unpkg': {
      base = ''
      break
    }
    default: {
      base = '/'
    }
  }
  return {
    base,
    html: {
      template: 'src/index.html',
      favicon: '',
      title: '',
      tags: [
        {
          pos: 'head',
          innerHTML: `if (/debug=1/.test(window.location.href)) {
            const script = document.createElement('script');
            script.src = 'https://yo-debug.yy.com/target.js';
            document.head.appendChild(script);
          }`,
          tagName: 'script',
        },
      ],
    },
    plugins: [
      ReactPlugin(),
      pluginRspackEmpShare({
        empRuntime: {
          runtime: {
            lib: `https://unpkg.yy.com/@empjs/share@3.6.0/output/sdk.js`,
          },
          framework: {
            libs: [`https://unpkg.yy.com/@empjs/cdn-react@0.18.0/dist/reactRouter.${store.mode}.umd.js`],
            global: 'EMP_ADAPTER_REACT',
          },
          setExternals(o) {
            externalReact(o, 'EMP_ADAPTER_REACT')
            return o
          },
        },
        exposes: {
          './render': './src/pages/Astro/render',
          './astro': './src/Astro/index',
          './astroStore': './src/Astro/astroStore',
          './astroConfig': './src/config/index',
        },
        dts: env === 'unpkg',
        manifest: true,
        name: 'Astro_Render',
      }),
    ],
    build: {
      polyfill: {
        entryCdn: 'https://unpkg.yy.com/@empjs/polyfill@0.0.2/dist/es.js',
      },
      sourcemap: true,
    },
    server: {
      port: 8000,
      open: false,
      https: true,
      client: {
        overlay: false,
      },
    },
    debug: {
      // showRsconfig: true,
      // clearLog: false,
    },
  }
})
